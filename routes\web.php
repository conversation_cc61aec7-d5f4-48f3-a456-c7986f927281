<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\TalentController;
use App\Http\Controllers\ProductsController;
use App\Http\Controllers\Dashboard\ClientDashboardController;
use App\Http\Controllers\Dashboard\ProfessionalDashboardController;
use App\Http\Controllers\Dashboard\SellerDashboardController;
use App\Http\Controllers\ApplySellerController;
use App\Http\Controllers\ApplyProfessionalController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Services Pages
Route::get('/services', [ServicesController::class, 'index'])->name('services');
Route::get('/services/{slug}', [ServicesController::class, 'show'])->name('services.show');

// Talent Pages
Route::get('/talent', [TalentController::class, 'index'])->name('talent');
Route::get('/talent/{slug}', [TalentController::class, 'show'])->name('talent.show');

// Products Pages
Route::get('/products', [ProductsController::class, 'index'])->name('products');
Route::get('/products/{slug}', [ProductsController::class, 'show'])->name('products.show');

// Store Pages
Route::get('/stores', [StoreController::class, 'index'])->name('stores');
Route::get('/store/{slug}', [StoreController::class, 'show'])->name('store.show');

// Authentication Routes are now handled by Laravel Fortify

// Apply Seller Routes
Route::prefix('apply/seller')->middleware(['auth'])->group(function () {
    Route::get('/', [ApplySellerController::class, 'showApplicationForm'])->name('apply.seller.form');
    Route::post('/', [ApplySellerController::class, 'submitApplication'])->name('apply.seller.submit');
    Route::get('/status', [ApplySellerController::class, 'showApplicationStatus'])->name('apply.seller.status');
});

// Apply Professional Routes
Route::prefix('apply/professional')->middleware(['auth'])->group(function () {
    Route::get('/', [ApplyProfessionalController::class, 'showApplicationForm'])->name('apply.professional.form');
    Route::post('/', [ApplyProfessionalController::class, 'submitApplication'])->name('apply.professional.submit');
    Route::get('/status', [ApplyProfessionalController::class, 'showApplicationStatus'])->name('apply.professional.status');
});

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'admin'])->name('admin.')->group(function () {
    // Seller Applications
    Route::get('/seller-applications', [App\Http\Controllers\Admin\SellerApplicationController::class, 'index'])->name('seller-applications.index');
    Route::get('/seller-applications/{id}', [App\Http\Controllers\Admin\SellerApplicationController::class, 'show'])->name('seller-applications.show');
    Route::post('/seller-applications/{id}/approve', [App\Http\Controllers\Admin\SellerApplicationController::class, 'approve'])->name('seller-applications.approve');
    Route::post('/seller-applications/{id}/reject', [App\Http\Controllers\Admin\SellerApplicationController::class, 'reject'])->name('seller-applications.reject');

    // Professional Applications
    Route::get('/professional-applications', [App\Http\Controllers\Admin\ProfessionalApplicationController::class, 'index'])->name('professional-applications.index');
    Route::get('/professional-applications/{id}', [App\Http\Controllers\Admin\ProfessionalApplicationController::class, 'show'])->name('professional-applications.show');
    Route::post('/professional-applications/{id}/approve', [App\Http\Controllers\Admin\ProfessionalApplicationController::class, 'approve'])->name('professional-applications.approve');
    Route::post('/professional-applications/{id}/reject', [App\Http\Controllers\Admin\ProfessionalApplicationController::class, 'reject'])->name('professional-applications.reject');
    Route::get('/professional-applications/{id}/resume', [App\Http\Controllers\Admin\ProfessionalApplicationController::class, 'downloadResume'])->name('professional-applications.resume');
});

// Dashboard Routes - Protected by auth middleware
Route::middleware(['auth'])->group(function () {
    // Client Dashboard Routes
    Route::prefix('dashboard/client')->middleware(['client'])->group(function () {
        Route::get('/', [ClientDashboardController::class, 'index'])->name('dashboard.client');

        // Projects routes
        Route::get('/projects', [ClientDashboardController::class, 'projects'])->name('dashboard.client.projects');
        Route::get('/projects/create', [ClientDashboardController::class, 'createProject'])->name('dashboard.client.projects.create');
        Route::get('/projects/{id}', [ClientDashboardController::class, 'showProject'])->name('dashboard.client.projects.show');
        Route::post('/projects', [ClientDashboardController::class, 'storeProject'])->name('dashboard.client.projects.store');

        // Messages routes
        Route::get('/messages', [ClientDashboardController::class, 'messages'])->name('dashboard.client.messages');
        Route::get('/messages/create', [ClientDashboardController::class, 'createMessage'])->name('dashboard.client.messages.create');
        Route::post('/messages', [ClientDashboardController::class, 'storeMessage'])->name('dashboard.client.messages.store');
        Route::get('/messages/check-new/{messageId}', [ClientDashboardController::class, 'checkNewMessages'])->name('dashboard.client.messages.check-new');
        Route::get('/messages/{id}', [ClientDashboardController::class, 'showMessage'])->name('dashboard.client.messages.show');

        // Other routes
        Route::get('/favorites', [ClientDashboardController::class, 'favorites'])->name('dashboard.client.favorites');
        Route::post('/favorites/toggle/{professional_id}', [ClientDashboardController::class, 'toggleFavorite'])->name('dashboard.client.favorites.toggle');

        // Orders routes
        Route::get('/orders', [ClientDashboardController::class, 'orders'])->name('dashboard.client.orders');
        Route::get('/orders/{id}', [ClientDashboardController::class, 'showOrder'])->name('dashboard.client.orders.show');
        Route::get('/orders/{id}/download', [ClientDashboardController::class, 'downloadOrder'])->name('dashboard.client.orders.download');

        Route::get('/profile', [ClientDashboardController::class, 'profile'])->name('dashboard.client.profile');
        Route::get('/settings', [ClientDashboardController::class, 'settings'])->name('dashboard.client.settings');
        Route::put('/profile/update', [ClientDashboardController::class, 'updateProfile'])->name('client.updateProfile');
    });

    // Professional Dashboard Routes
    Route::prefix('dashboard/professional')->middleware(['professional'])->group(function () {
        Route::get('/', [ProfessionalDashboardController::class, 'index'])->name('dashboard.professional');

        // Projects routes
        Route::get('/projects', [ProfessionalDashboardController::class, 'projects'])->name('dashboard.professional.projects');
        Route::get('/projects/{id}', [ProfessionalDashboardController::class, 'showProject'])->name('dashboard.professional.projects.show');

        // Messages routes
        Route::get('/messages', [ProfessionalDashboardController::class, 'messages'])->name('dashboard.professional.messages');
        Route::get('/messages/create', [ProfessionalDashboardController::class, 'createMessage'])->name('dashboard.professional.messages.create');
        Route::post('/messages', [ProfessionalDashboardController::class, 'storeMessage'])->name('dashboard.professional.messages.store');
        Route::get('/messages/check-new/{messageId}', [ProfessionalDashboardController::class, 'checkNewMessages'])->name('dashboard.professional.messages.check-new');
        Route::get('/messages/{id}', [ProfessionalDashboardController::class, 'showMessage'])->name('dashboard.professional.messages.show');

        // Portfolio routes
        Route::get('/portfolio', [ProfessionalDashboardController::class, 'portfolio'])->name('dashboard.professional.portfolio');
        Route::get('/portfolio/create', [ProfessionalDashboardController::class, 'createPortfolioItem'])->name('dashboard.professional.portfolio.create');
        Route::post('/portfolio', [ProfessionalDashboardController::class, 'storePortfolioItem'])->name('dashboard.professional.portfolio.store');
        Route::get('/portfolio/{id}', [ProfessionalDashboardController::class, 'showPortfolioItem'])->name('dashboard.professional.portfolio.show');
        Route::get('/portfolio/{id}/edit', [ProfessionalDashboardController::class, 'editPortfolioItem'])->name('dashboard.professional.portfolio.edit');
        Route::put('/portfolio/{id}', [ProfessionalDashboardController::class, 'updatePortfolioItem'])->name('dashboard.professional.portfolio.update');
        Route::delete('/portfolio/{id}', [ProfessionalDashboardController::class, 'destroyPortfolioItem'])->name('dashboard.professional.portfolio.destroy');

        // Product routes
        Route::get('/products', [ProfessionalDashboardController::class, 'products'])->name('dashboard.professional.products');
        Route::get('/products/create', [ProfessionalDashboardController::class, 'createProduct'])->name('dashboard.professional.products.create');
        Route::post('/products', [ProfessionalDashboardController::class, 'storeProduct'])->name('dashboard.professional.products.store');
        Route::get('/products/{id}', [ProfessionalDashboardController::class, 'showProduct'])->name('dashboard.professional.products.show');
        Route::get('/products/{id}/edit', [ProfessionalDashboardController::class, 'editProduct'])->name('dashboard.professional.products.edit');
        Route::put('/products/{id}', [ProfessionalDashboardController::class, 'updateProduct'])->name('dashboard.professional.products.update');
        Route::delete('/products/{id}', [ProfessionalDashboardController::class, 'destroyProduct'])->name('dashboard.professional.products.destroy');

        // Service routes
        Route::get('/services', [ProfessionalDashboardController::class, 'services'])->name('dashboard.professional.services');
        Route::get('/services/create', [ProfessionalDashboardController::class, 'createService'])->name('dashboard.professional.services.create');
        Route::post('/services', [ProfessionalDashboardController::class, 'storeService'])->name('dashboard.professional.services.store');
        Route::get('/services/{id}', [ProfessionalDashboardController::class, 'showService'])->name('dashboard.professional.services.show');
        Route::get('/services/{id}/edit', [ProfessionalDashboardController::class, 'editService'])->name('dashboard.professional.services.edit');
        Route::put('/services/{id}', [ProfessionalDashboardController::class, 'updateService'])->name('dashboard.professional.services.update');
        Route::delete('/services/{id}', [ProfessionalDashboardController::class, 'destroyService'])->name('dashboard.professional.services.destroy');

        // Earnings route
        Route::get('/earnings', [ProfessionalDashboardController::class, 'earnings'])->name('dashboard.professional.earnings');

        // Profile and settings routes
        Route::get('/profile', [ProfessionalDashboardController::class, 'profile'])->name('dashboard.professional.profile');
        Route::get('/settings', [ProfessionalDashboardController::class, 'settings'])->name('dashboard.professional.settings');
        Route::put('/profile/update', [ProfessionalDashboardController::class, 'updateProfile'])->name('professional.updateProfile');

        // Quick action routes
        Route::get('/availability', [ProfessionalDashboardController::class, 'updateAvailability'])->name('dashboard.professional.availability');
        Route::post('/availability', [ProfessionalDashboardController::class, 'storeAvailability'])->name('dashboard.professional.availability.store');

        Route::get('/pricing', [ProfessionalDashboardController::class, 'updatePricing'])->name('dashboard.professional.pricing');
        Route::post('/pricing', [ProfessionalDashboardController::class, 'storePricing'])->name('dashboard.professional.pricing.store');

        Route::get('/schedule', [ProfessionalDashboardController::class, 'scheduleAvailability'])->name('dashboard.professional.schedule');
        Route::post('/schedule', [ProfessionalDashboardController::class, 'storeSchedule'])->name('dashboard.professional.schedule.store');
    });

    // Seller Dashboard Routes
    Route::prefix('dashboard/seller')->middleware(['seller'])->group(function () {
        Route::get('/', [SellerDashboardController::class, 'index'])->name('dashboard.seller');

        // Product routes
        Route::get('/products', [SellerDashboardController::class, 'products'])->name('dashboard.seller.products');
        Route::get('/products/create', [SellerDashboardController::class, 'createProduct'])->name('dashboard.seller.products.create');
        Route::post('/products', [SellerDashboardController::class, 'storeProduct'])->name('dashboard.seller.products.store');
        Route::get('/products/{id}', [SellerDashboardController::class, 'showProduct'])->name('dashboard.seller.products.show');
        Route::get('/products/{id}/edit', [SellerDashboardController::class, 'editProduct'])->name('dashboard.seller.products.edit');
        Route::put('/products/{id}', [SellerDashboardController::class, 'updateProduct'])->name('dashboard.seller.products.update');
        Route::delete('/products/{id}', [SellerDashboardController::class, 'destroyProduct'])->name('dashboard.seller.products.destroy');

        // Earnings route
        Route::get('/earnings', [SellerDashboardController::class, 'earnings'])->name('dashboard.seller.earnings');

        // Profile route
        Route::get('/profile', [SellerDashboardController::class, 'profile'])->name('dashboard.seller.profile');
    });
});
